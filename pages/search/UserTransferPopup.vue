<template>
  <div
    v-if="visible"
    class="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-40"
    @click.self="close"
  >
    <div class="relative w-[90vw] max-w-[600px] min-h-[300px] max-h-[80vh] bg-white rounded-3 overflow-y-auto scroll-setting">
      <!-- 骨架图加载状态 -->
      <div v-if="loading">
        <!-- 顶部区域骨架 - 使用实际背景色 -->
        <div class="relative w-full h-[175px] back-linear flex items-center justify-center mb-4">
          <div class="w-[340px] flex items-center justify-between">
            <!-- 左侧公司骨架 -->
            <div class="fx-cer flex-col justify-center gap-2">
              <div class="w-12.5 h-12.5 rounded-full bg-white/20 skeleton-loading"></div>
              <div class="w-20 h-3 bg-white/20 rounded skeleton-loading"></div>
            </div>
            <!-- 中间薪水骨架 -->
            <div class="flex flex-1 items-center flex-col justify-center gap-2">
              <div class="w-32 h-8 bg-white/20 rounded skeleton-loading"></div>
              <div class="w-[90px] h-4 bg-white/20 rounded skeleton-loading"></div>
            </div>
            <!-- 右侧公司骨架 -->
            <div class="fx-cer flex-col justify-center gap-2">
              <div class="w-12.5 h-12.5 rounded-full bg-white/20 skeleton-loading"></div>
              <div class="w-20 h-3 bg-white/20 rounded skeleton-loading"></div>
            </div>
          </div>
        </div>

        <!-- 用户信息骨架 -->
        <div class="px-6 relative">
          <!-- 头像骨架 - 进一步上移，与姓名保持距离 -->
          <div class="absolute top-[-75px] left-5 w-20 h-20 rounded-full bg-gray-200 skeleton-loading"></div>

          <div class="mt-8 space-y-4">
            <!-- 姓名和按钮骨架 -->
            <div class="flex justify-between items-start">
              <div class="space-y-2 mt-4">
                <div class="w-48 h-6 bg-gray-200 rounded skeleton-loading"></div>
                <!-- 社交链接骨架 - 合并为一个 -->
                <div class="w-32 h-8 bg-gray-200 rounded skeleton-loading"></div>
              </div>
              <div class="flex gap-2 -mt-2">
                <div class="w-16 h-8 bg-gray-200 rounded skeleton-loading"></div>
                <div class="w-20 h-8 bg-gray-200 rounded skeleton-loading"></div>
              </div>
            </div>

            <!-- 基本信息卡片骨架 -->
            <div class="w-full h-16 bg-gray-100 rounded-2 skeleton-loading"></div>

            <!-- 描述骨架 -->
            <div class="space-y-2">
              <div class="w-full h-4 bg-gray-200 rounded skeleton-loading"></div>
              <div class="w-4/5 h-4 bg-gray-200 rounded skeleton-loading"></div>
              <div class="w-3/5 h-4 bg-gray-200 rounded skeleton-loading"></div>
            </div>

            <!-- 标签骨架 - 合并为一个整体 -->
            <div class="w-60 h-6 bg-gray-200 rounded skeleton-loading"></div>

            <!-- 工作经历等详细内容区域 - 适应整体滚动 -->
            <div class="w-full h-[200px] bg-gray-100 rounded-xl skeleton-loading"></div>
          </div>

          <!-- 底部空间 - 为footer留出空间 -->
          <div class="h-[40px]"></div>
        </div>
      </div>

      <!-- 实际内容 -->
      <div v-else>
        <!-- 顶部导航 -->
        <div class="relative w-full h-[175px] back-linear flex items-center justify-center mb-4">
        <img src="~/assets/image/bgTri.png" alt="" class="absolute top-0 left-0 w-40 h-40" />
        <img
          src="~/assets/image/bgCircle.png"
          alt=""
          class="absolute bottom-[-80px] right-0 w-40 h-40"
        />
        <div class="w-[340px] flex items-center justify-between">
          <div class="fx-cer flex-col justify-center gap-2 text-sm text-[#C6C6C6]">
            <img
              :src="user?.from_company_logo_url || defaultCompany"
              class="w-12.5 h-12.5  rounded-full"
            />
            <span class="w-25 line-clamp-1 text-center">{{ user?.from_company }}</span>
          </div>
          <div class="flex flex-1 items-center flex-col justify-center">
            <span v-if="extractMidDollarValue(user?.salary) === 'Unknown'"
              class="text-[20px] font-500 text-[#FFEEE7]"
              style="font-family: Poppins;"
            >
              Unknown
            </span>
            <span v-else class="text-[32px] font-500 text-[#FFEEE7]">
              <span style="font-family: 'UDC 1.04';">{{ extractMidDollarValue(user?.salary).replace(/[KM]/g, '') }}</span><span style="font-family: Poppins; margin-left: -2px;">{{ extractMidDollarValue(user?.salary).match(/[KM]/g)?.join('') || '' }}</span>
            </span>
            <img src="~/assets/image/fromto2.svg" alt="" class="w-[90px]" />
          </div>
          <div class="fx-cer flex-col justify-center gap-2 text-sm text-[#C6C6C6]">
            <img
              :src="user?.to_company_logo_url || defaultCompany"
              class="w-12.5 h-12.5 rounded-full"
            />
            <span class="w-25 line-clamp-1 text-center">{{ user?.to_company }}</span>
          </div>
        </div>
        <!-- time & source -->
        <div
          class="absolute right-10 bottom-4 flex items-center text-xs text-[#6F6F6F] dark:text-gray-400 gap-2"
        >
          <div class="flex items-center gap-1">
            <img src="~/assets/image/schedule.svg" alt="" class="w-4 h-4" />
            <span>{{ formatDate(user?.created_at) }}</span>
          </div>
          <span class="w-[1px] h-3 border-l border-[#414141]"></span>
          <div class="fx-cer justify-end gap-1">
            Source: <span class="max-w-15 text-ellipsis line-clamp-1">{{ user?.query }}</span>
          </div>
        </div>
      </div>
      <div class="px-6 relative">
        <img :src="user?.avatar_url || defaultAvator" class="absolute top-[-50px] left-5 w-20 h-20 rounded-full object-cover" />
        <!-- 用户信息 -->
        <div class="mb-4">
          <div class="relative fx-cer justify-between">
            <div class="mt-8">
              <h2 class="text-lg font-semibold text-[#3C3C3C]">
                {{ user?.person_name }} <span v-if="user?.nameCN">({{ user?.nameCN }})</span>
              </h2>
            </div>
            <div class="absolute top-0 right-0 fx-cer gap-1">
              <button
                class="relative fx-cer gap-1 h-8 min-w-[34px] p-2 text-sm border border-black bg-white rounded hover:bg-gray-200"
                @click="triggerPlusOne"
              >
                <!-- +1 动画：在按钮正上方浮动 -->
                <transition name="float">
                  <span
                    v-if="showPlusOne"
                    class="absolute left-1/2 top-0 left-[30%] -translate-x-1/2 -translate-y-full text-red-500 text-sm font-bold pointer-events-none select-none"
                  >
                    +1
                  </span>
                </transition>
                <img v-if="!user?.isLiked" src="~/assets/image/like1.svg" alt="" class="w-4 h-4" />
                <img v-else src="~/assets/image/like2.svg" alt="" class="w-4 h-4" />
                <span v-if="user?.like" :class="{ 'text-[#CB7C5D]': user?.isLiked }">{{
                  user?.like
                }}</span>
              </button>
              <button
                class="px-3 py-1 h-8 min-w-[85px] text-sm bg-black text-white rounded fx-cer gap-1"
                @click.stop="onShare"
              >
                <img src="~/assets/image/share1.svg" alt="" class="w-4 h-4" />
                <span>Share</span>
              </button>
            </div>
          </div>
          <!-- icon 列表 -->
          <div class="flex items-center mt-2 gap-3">
            <a
              v-if="user?.tweet_url"
              :href="user.tweet_url"
              target="_blank"
              class="w-8 h-8 border border-black rounded-2 p-2"
            >
              <img src="/image/header-x.svg" alt="" class="w-4 h-4" />
            </a>
            <a
              v-if="user?.linkedin_url"
              :href="user.linkedin_url"
              target="_blank"
              class="w-8 h-8 rounded-2"
            >
              <img src="~/assets/image/linkedin.svg" alt="" />
            </a>
            <a
              v-if="user?.github_url"
              :href="user.github_url"
              target="_blank"
              class="w-8 h-8 border rounded-2"
            >
              <img src="~/assets/image/github4.svg" alt="" />
            </a>
            <a
              v-if="user?.scholar"
              :href="user.scholar"
              target="_blank"
              class="w-8 h-8 border border-black rounded-2 p-1"
            >
              <img src="~/assets/image/mortarboard1.svg" alt="" class="w-5 h-5" />
            </a>
          </div>
        </div>
        <div class="rounded-xl">
          <!-- 基本信息 -->
          <div class="w-full fx-cer items-center rounded-2 mb-4 overflow-hidden bg-[#F9F9F9]">
            <div class="p-2 text-center flex-1">
              <p class="font-semibold text-4 text-[#3C3C3C]">{{ user?.age }}</p>
              <p class="text-3 text-[#6F6F6F] fx-cer justify-center gap-2">
                <img src="~/assets/image/age1.svg" alt="" class="w-3 h-3" />
                <span>Age</span>
              </p>
            </div>
            <span class="border-r h-8 border-[#DCDCDC]"></span>
            <div class="p-2 text-center flex-1">
              <p class="font-500 text-[#FFEEE7]">
                <span v-if="extractMidDollarValue(user?.salary) === 'Unknown'"
                  class="text-[20px]"
                  style="font-family: Poppins;"
                >
                  Unknown
                </span>
                <span v-else class="text-[32px]">
                  <span style="font-family: 'UDC 1.04';">{{ extractMidDollarValue(user?.salary).replace(/[KM]/g, '') }}</span><span style="font-family: Poppins; margin-left: -2px;">{{ extractMidDollarValue(user?.salary).match(/[KM]/g)?.join('') || '' }}</span>
                </span>
              </p>
              <p class="text-3 text-[#6F6F6F] fx-cer justify-center gap-2">
                <img src="~/assets/image/annual.svg" alt="" class="w-3 h-3" />
                <span>Annual</span>
              </p>
            </div>
          </div>
          <!-- 简介 -->
          <p class="text-sm text-[#3C3C3C] mb-3 leading-relaxed dark:text-black!">{{ user?.talent_description }}</p>
          <!-- 标签 -->
          <div v-if="user?.tags" class="flex flex-wrap gap-2 mb-4">
            <span
              v-for="tag in user?.tags"
              :key="tag"
              class="h-6 px-2 py-1 text-xs text-center border border-[#DDD] bg-[#F8F8F8] rounded"
              >{{ tag }}</span
            >
          </div>

          <!-- 工作经历 -->
          <div v-if="user?.work_experience && user.work_experience.length > 0" class="mb-4">
            <h3 class="font-semibold mb-2 fx-cer gap-2">
              <img src="~/assets/image/briefcase.svg" alt="" />
              <span class="dark:text-black!">Work Experience</span>
            </h3>
            <ul class="space-y-2">
              <li
                v-for="(item, i) in user?.work_experience"
                :key="i"
                class="text-sm fx-cer gap-2 h-15"
              >
                <div class="relative h-full fx-cer flex-col w-2 justify-center">
                  <span
                    v-if="i === 0"
                    class="absolute top-3 left-0 w-2 h-2 border rounded-full border-[#CB7C5D] bg-white z-1"
                  ></span>
                  <span
                    v-else
                    class="absolute top-4 w-2 h-2 bg-[#ccc] rounded-full z-1"
                    :class="{
                      'top-3': user?.work_experience && i === user?.work_experience?.length - 1,
                    }"
                  ></span>
                  <span
                    v-if="user?.work_experience && i !== user?.work_experience?.length - 1"
                    class="absolute top-3 h-18 mt-2 border-l border-[#ccc] border-dashed"
                  ></span>
                </div>
                <div class="w-full">
                  <p class="fx-cer justify-between border-b border-[#EEEEEE]">
                    <b class="fx-cer gap-2 border-b border-[#C88D75] pl-2 pr-4 h-8">
                      <img :src="item.company_logo_url || defaultCompany" alt="" class="w-6 h-6 rounded-full object-cover" />
                      <span class="dark:text-black!">{{ item.company }}</span>
                    </b>
                    <span class="text-[#6F6F6F] text-xs dark:text-black!">{{ item.from }}-{{ item.to }}</span>
                  </p>
                  <p class="text-[#3C3C3C] text-xs font-400 leading-8 dark:text-black!">{{ item.position }}</p>
                </div>
              </li>
            </ul>
          </div>

          <!-- 教育经历 -->
          <div v-if="user?.education && user?.education.length > 0" class="mb-4">
            <h3 class="font-semibold mb-2 fx-cer gap-2">
              <img src="~/assets/image/mortarboard1.svg" alt="" class="w-4 h-4" />
              <span class="dark:text-black!">Education</span>
            </h3>
            <ul class="space-y-2 text-sm">
              <li v-for="(edu, i) in user?.education" :key="i" class="text-sm fx-cer gap-2 h-8">
                <div class="relative h-full fx-cer flex-col w-2 justify-center">
                  <span
                    v-if="i === 0"
                    class="absolute top-3 w-2 h-2 border rounded-full border-[#CB7C5D] bg-white z-1"
                  ></span>
                  <span
                    v-else
                    class="absolute top-3 w-2 h-2 bg-[#ccc] rounded-full z-1"
                    :class="{
                      'top-3': user?.education && i === user?.education.length - 1,
                    }"
                  ></span>
                  <span
                    v-if="user?.education && i !== user?.education.length - 1"
                    class="absolute top-3 h-10 mt-2 border-l border-[#ccc] border-dashed"
                  ></span>
                </div>
                <p class="fx-cer gap-2 dark:text-black!">
                  <img src="~/assets/image/Ellipse191.svg" alt="" />
                  <b>{{ edu.school }}</b
                  >, {{ edu.major }} <span v-if="edu.time">({{ edu.time }})</span>
                </p>
              </li>
            </ul>
          </div>

          <!-- 成就 -->
          <div v-if="user?.major_achievement && user?.major_achievement.length > 0" class="mb-4">
            <h3 class="font-semibold mb-2 fx-cer gap-2">
              <img src="~/assets/image/medal1.svg" alt="" />
              <span class="dark:text-black!">Major Achievements</span>
            </h3>
            <ul class="list-disc list-inside space-y-1 text-sm text-gray-800">
              <li
                v-for="(item, i) in user?.major_achievement"
                :key="i"
                class="text-sm fx-cer gap-2 h-12 dark:text-black!"
              >
                <div class="relative h-full fx-cer flex-col w-2 justify-center">
                  <span
                    v-if="i === 0"
                    class="absolute top-3 left-0 w-2 h-2 border rounded-full border-[#CB7C5D] bg-white z-2"
                  ></span>
                  <span
                    v-else
                    class="absolute top-3 left-0 w-2 h-2 bg-[#ccc] rounded-full z-2"
                    :class="{
                      'top-3': user?.major_achievement && i === user?.major_achievement?.length - 1,
                    }"
                  ></span>
                  <span
                    v-if="user?.major_achievement && i !== user?.major_achievement.length - 1"
                    class="absolute top-3 left-1 h-12 mt-2 border-l border-[#ccc] border-dashed"
                  ></span>
                </div>
                <span
                  ><b>{{ item.title }}</b
                  >-{{ item.description }}</span
                >
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="fx-cer justify-center gap-1 text-center text-sm text-[#B8B8B8] py-2">
        Built with <img src="~/assets/image/Like.svg" alt="" class="w-3.5 h-3.5" /> by DINQ
      </div>
      </div>

      <!-- 关闭按钮 -->
      <button class="absolute right-4 top-4 text-xl font-bold bg-transparent" @click="close">
        <img src="~/assets/image/Close-small2.svg" class="w-6 h-6 bg-transparent" />
      </button>
    </div>
    <ShareModal
      v-if="user"
      :visible="showShareModal"
      :user="user"
      @close="showShareModal = false"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue'
  import { extractMidDollarValue } from '~/pages/search/utils'
  import { getMoveInfo, getLikeInfo } from '~/api'
  import type { UserCard } from '~/api/types'
  import ShareModal from './ShareModal.vue'
  const showPlusOne = ref(false)
  const showShareModal = ref(false)
  const { currentUser } = useFirebaseAuth()
  import defaultCompany from '~/assets/image/defaultCompany.png'
  import defaultAvator from '~/assets/image/defaultAvator.png'

  const onShare = () => {
    if (!user.value) return
    showShareModal.value = true
  }

  const triggerPlusOne = () => {
    if (!user?.value?.isLiked) {
      showPlusOne.value = true
    }
    fetchLiked()
    setTimeout(() => {
      showPlusOne.value = false
    }, 600) // 动画持续时间
  }
  const props = defineProps<{
    moveId: number
    visible: boolean
    onClose?: () => void
  }>()

  const emit = defineEmits(['close'])

  interface User extends UserCard {
    isLiked?: boolean
    like?: number
    nameCN?: string
    tags?: string[]
    linkedin_url?: string
    github_url?: string
    scholar?: string
  }
  const user = ref<User>()
  const loading = ref(false)

  const close = () => {
    emit('close')
    props.onClose?.()
  }

  const fetchUser = async () => {
    loading.value = true
    user.value = undefined
    if (!props.moveId) return
    try {
      await getMoveInfo(props.moveId, { headers: { Userid: currentUser.value?.uid || '' } }).then(
        (res: { data: UserCard }) => {
          // 安全地解析 JSON 字符串，如果已经是对象则直接使用
          const major_achievement = typeof res.data.major_achievement === 'string'
            ? JSON.parse(res.data.major_achievement)
            : res.data.major_achievement
          const education = typeof res.data.education === 'string'
            ? JSON.parse(res.data.education)
            : res.data.education
          const work_experience = typeof res.data.work_experience === 'string'
            ? JSON.parse(res.data.work_experience)
            : res.data.work_experience
          console.log('education', education)
          user.value = {
            ...res.data,
            major_achievement,
            education,
            work_experience,
          } as User
        }
      )
    } finally {
      loading.value = false
    }
  }

  const fetchLiked = async () => {
    getLikeInfo(
      // { move_id: props.moveId },
      props.moveId,
      { headers: { Userid: currentUser.value?.uid || '' } }
    ).then(res => {
      if (user.value) {
        user.value = {
          ...user.value,
          isLiked: res.data.is_liked,
          like: res.data.like_count,
        } as User
      }
    })
  }
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-US', {
      dateStyle: 'long',
      // timeStyle: 'short',
    })
  }

  onMounted(() => {
    fetchUser()
  })

  watch(
    () => props.moveId,
    newMoveId => {
      if (newMoveId) {
        fetchUser()
      }
    }
  )
</script>

<style scoped>
  .scroll-setting {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
  }

  .scroll-setting::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }

  .back-linear {
    background: linear-gradient(96deg, #281812 0.42%, #120f2a 73.12%);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }
  /* 向上浮动并淡出动画 */
  .float-enter-active,
  .float-leave-active {
    transition: all 0.6s ease;
  }
  .float-enter-from {
    opacity: 0;
    transform: translateY(0);
  }
  .float-enter-to {
    opacity: 1;
    transform: translateY(-20px);
  }
  .float-leave-from {
    opacity: 1;
    transform: translateY(-20px);
  }
  .float-leave-to {
    opacity: 0;
    transform: translateY(-40px);
  }

  /* 骨架图加载动画 */
  .skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading-animation 1.5s infinite;
  }

  /* 顶部区域的白色透明骨架动画 */
  .back-linear .skeleton-loading {
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading-animation 1.5s infinite;
  }

  @keyframes skeleton-loading-animation {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
</style>