<template>
  <div class="h-screen overflow-y-auto hide-scrollbar" ref="scrollContainer" @scroll="onScroll">
    <!-- 错误提示区域 - 固定在最顶部 -->
    <div v-if="showError" class="sticky top-0 z-30 px-4 pt-4">
      <div class="w-full max-w-[760px] mx-auto">
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-400">
                Access Required
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                {{ error }}
              </div>
            </div>
            <div class="ml-auto pl-3">
              <div class="-mx-1.5 -my-1.5">
                <button @click="clearError" class="inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600">
                  <span class="sr-only">Dismiss</span>
                  <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="px-4 pt-20 relative" :class="{ 'pt-15': isMobile }">
      <!-- 顶部 Banner -->
      <div class="fx-cer flex-col gap-2 p-4 relative z-10">
        <div class="text-[70px] font-600 font-alexandria">Poachub</div>
        <div class="text-6 font-600 text-[#555] font-alexandria">Top Talent Just Got Poached — Guess Who?</div>
        <div class="w-150 text-4 font-300 text-[#7A7A7A] text-center font-alexandria">
          Live updates on who's jumping ship in the tech world. Follow the biggest hires, exits, and
          power moves — as they happen.
        </div>
      </div>

      <!-- 背景图 -->
      <div class="absolute top-0 left-0 w-[436px] h-[469px] z-0 pointer-events-none">
        <img src="~/assets/image/bgTri.png" alt="" class="w-full h-full object-contain" />
      </div>
      <div class="absolute bottom-[-160px] right-0 w-[351px] h-[443px] z-0 pointer-events-none">
        <img src="~/assets/image/bgCircle.png" alt="" class="w-full h-full object-contain" />
      </div>

      <div class="p-2 rounded-lg relative z-10">
        <!-- 背景色遮罩 - 当滚动时显示，底部渐变效果 -->
        <div
          v-show="showBlurCard"
          class="fixed top-0 left-64 right-0 h-32 z-15 pointer-events-none transition-all duration-500 ease-out blur-mask-gradient"
          :class="{ 'opacity-100 translate-y-0': showBlurCard, 'opacity-0 -translate-y-4': !showBlurCard }"
        >
        </div>

        <!-- 标题 + 筛选下拉 - 固定在顶部 -->
        <div class="sticky top-8 bg-transparent p-4 flex items-center justify-center z-20">
          <motion.div
            :initial="{ opacity: 0, y: 30 }"
            :whileInView="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.8, ease: 'easeOut', delay: 0.4 }"
            :viewport="{ once: true }"
            class="toggle-wrapper"
          >
            <div class="toggle-container">
              <!-- 滑动背景指示器 -->
              <div class="toggle-slider" :class="{ 'slide-right': selected === 'Highest' }"></div>

              <div
                class="toggle-button"
                :class="{ selected: selected === 'Newest' }"
                @click="selectOption('Newest')"
              >
                Newest
              </div>
              <div
                class="toggle-button"
                :class="{ selected: selected === 'Highest' }"
                @click="selectOption('Highest')"
              >
                Highest
              </div>
            </div>
          </motion.div>
        </div>

        <!-- 卡片列表 -->
        <div class="px-4 space-y-4 pb-8">
          <!-- 卡片内容渲染（avatar + 描述 + 浏览量 + 时间） -->
          <div
            v-for="(card, index) in sortedCards"
            :key="card.id"
            class="w-full max-w-[1000px] m-auto p-4 border rounded-3 shadow cursor-pointer bg-white hover:bg-gray-100 dark:bg-[#2F3031] dark:border-none dark:hover:bg-gray-700 flex justify-between items-center"
            @click="() => openModal(card)"
          >
          <!-- 排名显示 (仅在Highest排序时显示) -->
          <div v-if="selected === 'Highest'" class="flex items-center justify-center w-7 h-8 mr-2">
            <!-- 前三名使用图片 -->
            <img v-if="index === 0" src="/image/top1.svg" alt="1st" class="w-7 h-8" />
            <img v-else-if="index === 1" src="/image/top2.svg" alt="2nd" class="w-7 h-8" />
            <img v-else-if="index === 2" src="/image/top3.svg" alt="3rd" class="w-7 h-8" />
            <!-- 第四名及以后使用数字 -->
            <span v-else class="text-sm font-semibold italic text-gray-600 dark:text-gray-300" style="font-family: Poppins; font-weight: 600; font-style: italic; font-size: 14px;">
              {{ index + 1 }}
            </span>
          </div>

          <div class="fx-cer flex-row gap-4 flex-1 min-w-0">
            <!-- 头像 -->
            <img
              :src="card.avatar_url || defaultAvator"
              alt="avatar"
              class="w-20 h-20 rounded-full object-cover ml-4 flex-shrink-0"
            />
            <div class="flex flex-col justify-left flex-1 min-w-0 max-w-[240px]">
              <div class="font-600 text-4 text-[#3C3C3C] dark:text-white truncate">{{ card.person_name }}</div>
              <div class="text-3 font-400 text-[#666] line-clamp-2 dark:text-white">
                {{ card.talent_description }}
              </div>
              <div class="text-3 font-400 text-[#6F6F6F] dark:text-white truncate">Age: {{ card.age }}</div>
            </div>
          </div>

          <!-- company info -->
          <div class="w-[240px] fx-cer flex-row gap-2 flex-shrink-0 ml-4">
            <div class="fx-cer flex-col justify-center gap-2">
              <img :src="card.from_company_logo_url || defaultCompany" alt=""  class="w-15 h-15 rounded-full flex-shrink-0"/>
              <div class="w-18 text-center text-[12px] text-[#7A7A7A] dark:text-white truncate">
                {{ card.from_company }}
              </div>
            </div>
            <img src="~/assets/image/fromto.svg" alt="" class="mb-6 flex-shrink-0" />
            <div class="fx-cer flex-col justify-center gap-2">
              <img :src="card.to_company_logo_url || defaultCompany" alt="" class="w-15 h-15 rounded-full flex-shrink-0"/>
              <div class="w-18 text-center text-[12px] text-[#7A7A7A] dark:text-white truncate">
                {{ card.to_company }}
              </div>
            </div>
          </div>

          <!-- 薪水 -->
          <div class="flex items-end flex-col gap-2 w-[200px] flex-shrink-0">
            <span
              class="h-15 leading-[60px] text-right w-full"
              :class="extractMidDollarValue(card.salary) === 'Unknown' ? 'unknown-salary-text' : 'text-9 font-600'"
            >{{
              extractMidDollarValue(card.salary)
            }}</span>
            <div class="flex items-center text-xs text-[#6F6F6F] dark:text-gray-400 gap-2 w-full justify-end">
              <div class="flex items-center gap-1">
                <img src="~/assets/image/schedule.svg" alt="" class="w-4 h-4 flex-shrink-0" />
                <span class="truncate">{{ formatDate(card.created_at) }}</span>
              </div>
              <span class="w-[1px] h-3 border-l border-[#C3C3C3] flex-shrink-0"></span>
              <div class="fx-cer justify-end gap-1 min-w-0">
                Source: <span class="max-w-15 truncate">{{ card.query }}</span>
              </div>
            </div>
          </div>
        </div>

          <!-- 骨架图加载状态 -->
          <div v-if="loading" class="space-y-4">
            <div
              v-for="n in 5"
              :key="n"
              class="w-full max-w-[1000px] m-auto p-4 border rounded-3 shadow bg-white dark:bg-[#2F3031] dark:border-none flex justify-between items-center animate-pulse"
            >
              <!-- 排名占位 (如果是Highest模式) -->
              <div v-if="selected === 'Highest'" class="w-7 h-8 mr-2">
                <div class="w-7 h-8 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>

              <!-- 左侧头像和信息骨架 -->
              <div class="fx-cer flex-row gap-4 flex-1 min-w-0">
                <!-- 头像骨架 -->
                <div class="w-20 h-20 rounded-full bg-gray-200 dark:bg-gray-600 ml-4 skeleton-loading flex-shrink-0"></div>
                <div class="flex flex-col justify-left flex-1 min-w-0 max-w-[240px] space-y-2">
                  <!-- 姓名骨架 -->
                  <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 skeleton-loading"></div>
                  <!-- 描述骨架 -->
                  <div class="h-3 bg-gray-200 dark:bg-gray-600 rounded w-full skeleton-loading"></div>
                  <div class="h-3 bg-gray-200 dark:bg-gray-600 rounded w-2/3 skeleton-loading"></div>
                  <!-- 年龄骨架 -->
                  <div class="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/3 skeleton-loading"></div>
                </div>
              </div>

              <!-- 公司信息骨架 -->
              <div class="w-[240px] fx-cer flex-row gap-2 flex-shrink-0 ml-4">
                <div class="fx-cer flex-col justify-center gap-2">
                  <div class="w-15 h-15 rounded-full bg-gray-200 dark:bg-gray-600 skeleton-loading flex-shrink-0"></div>
                  <div class="w-18 h-3 bg-gray-200 dark:bg-gray-600 rounded skeleton-loading"></div>
                </div>
                <div class="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded mb-6 skeleton-loading flex-shrink-0"></div>
                <div class="fx-cer flex-col justify-center gap-2">
                  <div class="w-15 h-15 rounded-full bg-gray-200 dark:bg-gray-600 skeleton-loading flex-shrink-0"></div>
                  <div class="w-18 h-3 bg-gray-200 dark:bg-gray-600 rounded skeleton-loading"></div>
                </div>
              </div>

              <!-- 薪水和时间信息骨架 -->
              <div class="flex items-end flex-col gap-2 w-[200px] flex-shrink-0">
                <div class="h-8 w-24 bg-gray-200 dark:bg-gray-600 rounded skeleton-loading ml-auto"></div>
                <div class="flex items-center gap-2 w-full justify-end">
                  <div class="h-3 w-16 bg-gray-200 dark:bg-gray-600 rounded skeleton-loading"></div>
                  <div class="w-[1px] h-3 bg-gray-200 dark:bg-gray-600 flex-shrink-0"></div>
                  <div class="h-3 w-20 bg-gray-200 dark:bg-gray-600 rounded skeleton-loading"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗 -->
    <UserTransferPopup v-if="selectedCard" :moveId="selectedCard.id" :visible="showModal" @close="showModal = false" />
  </div>
</template>

<script setup lang="ts">
  import { motion } from 'motion-v'
  import { ref, computed, onMounted } from 'vue'
  import UserTransferPopup from './UserTransferPopup.vue'
  import { getMoveList } from '~/api'
  import type { UserCard } from '~/api/types'
  import { extractMidDollarValue } from '~/pages/search/utils'
  import defaultCompany from '~/assets/image/defaultCompany.png'
  import defaultAvator from '~/assets/image/defaultAvator.png'

  const scrollContainer = ref<HTMLElement | null>(null)
  const isMobile = ref(false)
  // 添加选择框的状态和逻辑
  const selected = ref<'Newest' | 'Highest'>('Newest')

  // 毛玻璃卡片显示控制
  const showBlurCard = ref(false)
  const bannerHeight = ref(0)

  // 错误状态管理
  const error = ref('')
  const showError = ref(false)

  // Firebase Auth
  const { currentUser } = useFirebaseAuth()

  // 清除错误信息
  const clearError = () => {
    error.value = ''
    showError.value = false
  }

  // 显示错误信息
  const showErrorMessage = (message: string) => {
    error.value = message
    showError.value = true
  }

  // 切换选项的方法
  const selectOption = (option: 'Newest' | 'Highest') => {
    selected.value = option
  }

  onMounted(() => {
    isMobile.value = window.innerWidth < 768
  })

  interface Card extends UserCard {
    heat?: number // 添加热度字段用于排序
  }

  const cards = ref<Card[]>([])
  const page = ref(1)
  const loading = ref(false)
  const hasMoreData = ref(true) // 跟踪是否还有更多数据

  const sortedCards = computed(() => {
    if (selected.value === 'Highest') {
      return [...cards.value].sort((a, b) => {
        const salaryA = extractMidDollarValue(a.salary)
        const salaryB = extractMidDollarValue(b.salary)

        // 提取数值用于比较
        const getNumericValue = (salary: string | null) => {
          if (!salary || salary === 'Unknown') return null
          const match = salary.match(/\$(\d+(?:\.\d+)?)/)
          return match ? parseFloat(match[1]) : null
        }

        const numA = getNumericValue(salaryA)
        const numB = getNumericValue(salaryB)

        // 有金额的排在前面，按金额从高到低排序
        if (numA !== null && numB !== null) {
          return numB - numA
        }

        // 有金额的排在没金额的前面
        if (numA !== null && numB === null) return -1
        if (numA === null && numB !== null) return 1

        // 都没有金额的按时间排序（最新的在前）
        return b.created_at - a.created_at
      })
    }
    return [...cards.value].sort((a, b) => b.created_at - a.created_at)
  })

  const fetchMore = async () => {
    if (loading.value) return
    if (!hasMoreData.value) return // 如果没有更多数据，直接返回
    if (cards.value.length >= 100) return

    loading.value = true

    try {
      const res = await getMoveList(page.value, 20)
      console.log('API Response:', res.data)

      // 检查是否有数据
      if (res.data.moves && res.data.moves.length > 0) {
        // 从 res.data.moves 中获取数据，添加热度字段
        const newCards: Card[] = res.data.moves.map((item: UserCard) => ({
          ...item,
          heat: Math.floor(Math.random() * 1000) + 100, // 临时随机热度数据
        }))
        cards.value = [...cards.value, ...newCards]
        page.value++
      } else {
        console.log('No more data available')
        hasMoreData.value = false // 设置没有更多数据
      }
    } catch (error) {
      console.error('Error fetching move list:', error)
    } finally {
      loading.value = false
    }
  }

  const onScroll = () => {
    const el = scrollContainer.value
    if (!el) return
    
    // 检查是否需要显示毛玻璃卡片
    const scrollTop = el.scrollTop
    // 当滚动超过 banner 区域时显示毛玻璃卡片
    if (scrollTop > 300) { // banner 大概高度
      showBlurCard.value = true
    } else {
      showBlurCard.value = false
    }
    
    if (loading.value) return // 防止重复请求
    if (!hasMoreData.value) return // 如果没有更多数据，不触发请求

    const nearBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 100
    if (nearBottom) {
      fetchMore()
    }
  }

  const showModal = ref(false)
  const selectedCard = ref<Card | null>(null)
  const fullText = ref('')
  const animatedText = ref('')
  const typingIndex = ref(0)
  let interval: any = null

  const fetchDetailText = async (card: Card) => {
    await new Promise(r => setTimeout(r, 300))
    return `🚀 ${card.person_name} transferred from ${card.from_company} to ${card.to_company}.\n\nThis transition marks a significant milestone in their career with a salary of ${extractMidDollarValue(card.salary)}. We wish them success! 🎉`
  }

  const animateText = (text: string) => {
    animatedText.value = ''
    typingIndex.value = 0
    clearInterval(interval)
    interval = setInterval(() => {
      if (typingIndex.value < text.length) {
        animatedText.value += text[typingIndex.value]
        typingIndex.value++
      } else {
        clearInterval(interval)
      }
    }, 30)
  }

  const openModal = async (card: Card) => {
    // 清除之前的错误
    clearError()

    // 检查用户是否已登录
    if (!currentUser.value) {
      showErrorMessage('Please log in to view detailed talent information. Authentication is required to access our database.')
      return
    }

    selectedCard.value = card
    showModal.value = true
    fullText.value = await fetchDetailText(card)
    animateText(fullText.value)
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-US', {
      dateStyle: 'long',
      // timeStyle: 'short',
    })
  }

  onMounted(() => {
    fetchMore()
  })
</script>

<style scoped>
  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  .font-Poppins {
    font-family: 'Poppins', sans-serif;
  }

  .font-alexandria {
    font-family: 'Alexandria', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  .unknown-salary-text {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 20px;
    line-height: 36px;
    color: #A4A4A4;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .toggle-wrapper {
    display: flex;
    justify-content: center;
    width: 300px;
    height: 54px;
  }

  /* 从 index 页面移动过来的样式 - 新的切换按钮样式 */
  .toggle-container {
    display: flex;
    position: relative;
    justify-content: space-between;
    align-items: center;
    width: 300px;
    height: 50px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #d6d6d6;
    backdrop-filter: blur(34px);
    padding: 2px;
  }

  .toggle-button {
    width: 141px;
    height: 42px;
    text-align: center;
    cursor: pointer;
    position: relative;
    user-select: none;
    border-radius: 4px;
    font-family:
      'Alexandria',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    color: #000000;
    transition: color 0.3s ease;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
  }

  .toggle-button.selected {
    color: #ffffff;
  }

  .toggle-button:hover:not(.selected) {
    color: #333333;
  }

  .toggle-slider {
    width: 141px;
    height: 42px;
  }

  /* 滑动背景指示器 */
  .toggle-slider {
    position: absolute;
    width: 141px;
    height: 42px;
    background-color: #000000;
    border-radius: 4px;
    top: 50%;
    left: 4px;
    transform: translateY(-50%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
  }

  .toggle-slider.slide-right {
    transform: translateY(-50%) translateX(150px); /* 保持垂直居中 + 右移 */
  }

  .hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }

  /* 自定义 z-index 层级 */
  .z-15 {
    z-index: 15;
  }

  /* 深色模式下的毛玻璃效果优化 */
  .dark .blur-card-dark {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* 遮罩渐变样式 - 顶部85%不透明，底部15%渐变到透明 */
  .blur-mask-gradient {
    background: linear-gradient(to bottom, #EEEEEA 0%, #EEEEEA 85%, transparent 100%);
  }

  .dark .blur-mask-gradient {
    background: linear-gradient(to bottom, #0F0F0F 0%, #0F0F0F 85%, transparent 100%);
  }

  /* 骨架图加载动画 */
  .skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading-animation 1.5s infinite;
  }

  .dark .skeleton-loading {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }

  @keyframes skeleton-loading-animation {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
</style>